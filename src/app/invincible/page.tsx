'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Zap,
  Search,
  Play,
  Brain,
  PenTool,
  FileText,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Sparkles
} from 'lucide-react'
import { useRouter } from 'next/navigation'

interface ProgressData {
  stage: string
  progress: number
  message: string
  currentAgent: string
  data?: {
    queries?: string[]
    webSources?: number
    youtubeVideos?: number
    analysisComplete?: boolean
    wordCount?: number
  }
  timestamp: string
}



export default function InvinciblePage() {
  const [topic, setTopic] = useState('')
  const [wordCount, setWordCount] = useState(1500)
  const [tone, setTone] = useState('Professional and informative')
  const [targetAudience, setTargetAudience] = useState('')
  const [includeYouTube, setIncludeYouTube] = useState(true)
  const [additionalInstructions, setAdditionalInstructions] = useState('')
  
  const [isGenerating, setIsGenerating] = useState(false)
  const [progress, setProgress] = useState<ProgressData | null>(null)
  const [queries, setQueries] = useState<string[]>([])
  const [generatedContent, setGeneratedContent] = useState<any>(null)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const router = useRouter()

  const handleGenerate = async () => {
    if (!topic.trim()) return

    setIsGenerating(true)
    setProgress(null)
    setQueries([])
    setGeneratedContent(null)
    setIsRedirecting(false)

    try {
      // Start SSE connection with fetch
      const response = await fetch('/api/invincible/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic,
          wordCount,
          tone,
          targetAudience,
          includeYouTube,
          additionalInstructions
        })
      })

      if (!response.body) {
        throw new Error('No response body')
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      const readStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value)
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const data = JSON.parse(line.slice(6))

                  if (data.type === 'progress') {
                    setProgress(data.data)

                    // Update UI with real-time data
                    if (data.data.data?.queries) {
                      setQueries(data.data.data.queries)
                    }
                  } else if (data.type === 'completed') {
                    setGeneratedContent(data.data)
                    setIsGenerating(false)

                    // Save content to database and automatically redirect
                    if (data.data?.content) {
                      saveContentToDatabase(data.data.content)

                      // Store content for article view
                      localStorage.setItem('generatedArticle', data.data.content.content)
                      localStorage.setItem('articleTitle', data.data.content.title)
                      sessionStorage.setItem('generatedArticle', data.data.content.content)
                      sessionStorage.setItem('generatedTitle', data.data.content.title)

                      // Auto-redirect to article view after a short delay
                      setIsRedirecting(true)
                      setTimeout(() => {
                        router.push('/article-view')
                      }, 1500) // 1.5 second delay to show completion
                    }
                    return
                  } else if (data.type === 'error') {
                    console.error('Generation error:', data.data)
                    setIsGenerating(false)
                    return
                  }
                } catch (parseError) {
                  console.warn('Error parsing SSE data:', parseError)
                }
              }
            }
          }
        } catch (error) {
          console.error('Error reading stream:', error)
          setIsGenerating(false)
        }
      }

      readStream()

    } catch (error) {
      console.error('Error starting generation:', error)
      setIsGenerating(false)
    }
  }

  const saveContentToDatabase = async (content: any) => {
    try {
      const response = await fetch('/api/content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'invincible',
          title: content.title,
          content: content.content,
          wordCount: content.wordCount || 0,
          tone: tone,
          metadata: {
            sources: content.sources || [],
            videos: content.videos || [],
            processingTime: content.processingTime || 0,
            agentType: 'Invincible .1V'
          }
        })
      })

      if (response.ok) {
        const savedContent = await response.json()
        console.log('✅ Content saved to database:', savedContent.id)
        return savedContent
      } else {
        console.error('❌ Failed to save content to database')
      }
    } catch (error) {
      console.error('❌ Error saving content to database:', error)
    }
  }

  const viewGeneratedArticle = () => {
    if (generatedContent?.content) {
      // Store the article using the correct keys for article-view
      localStorage.setItem('generatedArticle', generatedContent.content.content)
      localStorage.setItem('articleTitle', generatedContent.content.title)

      // Also store in sessionStorage for immediate access
      sessionStorage.setItem('generatedArticle', generatedContent.content.content)
      sessionStorage.setItem('generatedTitle', generatedContent.content.title)

      router.push('/article-view')
    }
  }

  const getStageIcon = (stage: string) => {
    switch (stage) {
      case 'initialization': return <Zap className="w-5 h-5" />
      case 'research': return <Search className="w-5 h-5" />
      case 'youtube': return <Play className="w-5 h-5" />
      case 'analysis': return <Brain className="w-5 h-5" />
      case 'writing': return <PenTool className="w-5 h-5" />
      case 'completed': return <CheckCircle className="w-5 h-5" />
      case 'error': return <AlertCircle className="w-5 h-5" />
      default: return <Sparkles className="w-5 h-5" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-gradient-to-r from-violet-600 to-purple-600 rounded-2xl mr-4">
              <Zap className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-white">
              Invincible <span className="text-violet-400">.1V</span>
            </h1>
          </div>
          <p className="text-gray-300 text-lg">
            Supreme Content Writing Agent - Powered by Multi-Agent AI
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6"
          >
            <h2 className="text-2xl font-bold text-white mb-6">Content Configuration</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Topic *</label>
                <input
                  type="text"
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  placeholder="Enter your content topic..."
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-300 mb-2">Word Count</label>
                  <input
                    type="number"
                    value={wordCount}
                    onChange={(e) => setWordCount(parseInt(e.target.value) || 1500)}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-violet-500"
                  />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2">Tone</label>
                  <select
                    value={tone}
                    onChange={(e) => setTone(e.target.value)}
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-violet-500"
                  >
                    <option value="Professional and informative">Professional</option>
                    <option value="Casual and conversational">Casual</option>
                    <option value="Academic and formal">Academic</option>
                    <option value="Creative and engaging">Creative</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">Target Audience</label>
                <input
                  type="text"
                  value={targetAudience}
                  onChange={(e) => setTargetAudience(e.target.value)}
                  placeholder="e.g., Business professionals, Students, General audience"
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="includeYouTube"
                  checked={includeYouTube}
                  onChange={(e) => setIncludeYouTube(e.target.checked)}
                  className="mr-3 w-4 h-4 text-violet-600 bg-white/10 border-white/20 rounded focus:ring-violet-500"
                />
                <label htmlFor="includeYouTube" className="text-gray-300">
                  Include YouTube research and insights
                </label>
              </div>

              <div>
                <label className="block text-gray-300 mb-2">Additional Instructions</label>
                <textarea
                  value={additionalInstructions}
                  onChange={(e) => setAdditionalInstructions(e.target.value)}
                  placeholder="Any specific requirements or focus areas..."
                  rows={3}
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-violet-500"
                />
              </div>

              <button
                onClick={handleGenerate}
                disabled={!topic.trim() || isGenerating}
                className="w-full py-4 bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-xl font-semibold hover:scale-105 transition-transform disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              >
                {isGenerating ? 'Generating...' : 'Generate Supreme Content'}
              </button>
            </div>
          </motion.div>

          {/* Progress and Results */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Progress Display */}
            {(isGenerating || progress) && (
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
                <h3 className="text-xl font-bold text-white mb-4">Generation Progress</h3>
                
                {progress && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {getStageIcon(progress.stage)}
                        <span className="ml-2 text-white font-medium">{progress.currentAgent}</span>
                      </div>
                      <span className="text-violet-400">{progress.progress}%</span>
                    </div>
                    
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-violet-600 to-purple-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${progress.progress}%` }}
                      />
                    </div>
                    
                    <p className="text-gray-300">{progress.message}</p>
                    
                    {progress.data && (
                      <div className="text-sm text-gray-400">
                        {progress.data.webSources && <div>Web sources: {progress.data.webSources}</div>}
                        {progress.data.youtubeVideos && <div>YouTube videos: {progress.data.youtubeVideos}</div>}
                        {progress.data.wordCount && <div>Word count: {progress.data.wordCount}</div>}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Data Sources Display */}
            {queries.length > 0 && (
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
                <h3 className="text-xl font-bold text-white mb-4">Search Queries</h3>
                <div className="space-y-2">
                  {queries.map((query, index) => (
                    <div key={index} className="flex items-center text-gray-300">
                      <Search className="w-4 h-4 mr-2 text-violet-400" />
                      <span>{query}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Generated Content Preview */}
            {generatedContent && (
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
                <h3 className="text-xl font-bold text-white mb-4">Content Generated</h3>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-lg font-semibold text-violet-400">
                      {generatedContent.content?.title}
                    </h4>
                    <div className="text-sm text-gray-400 mt-2">
                      {generatedContent.content?.metadata?.wordCount} words • 
                      {generatedContent.content?.metadata?.readingTime} min read
                    </div>
                  </div>
                  
                  <div className="text-gray-300 text-sm">
                    Sources: {generatedContent.research?.totalSources || 0} • 
                    Processing time: {Math.round(generatedContent.processingTime / 1000)}s
                  </div>
                  
                  {isRedirecting ? (
                    <div className="w-full py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-semibold flex items-center justify-center">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        className="w-5 h-5 mr-2"
                      >
                        <Sparkles className="w-5 h-5" />
                      </motion.div>
                      Opening Article View...
                    </div>
                  ) : (
                    <button
                      onClick={viewGeneratedArticle}
                      className="w-full py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl font-semibold hover:scale-105 transition-transform flex items-center justify-center"
                    >
                      <FileText className="w-5 h-5 mr-2" />
                      View Generated Article
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </button>
                  )}
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  )
}

import { OpenRouterService } from '@/lib/openrouter'
import { GeminiService } from '@/lib/gemini'
import { 
  WebSearchResult, 
  YouTubeVideoData, 
  CompetitiveAnalysis, 
  ContentPattern,
  AgentConfig 
} from './types'

export class AnalysisAgent {
  private openRouter: OpenRouterService
  private gemini: GeminiService
  private agentName = 'Analysis Agent'
  private config: AgentConfig

  constructor(config?: Partial<AgentConfig>) {
    this.openRouter = new OpenRouterService()
    this.gemini = new GeminiService()
    this.config = {
      temperature: 0.3,
      maxTokens: 6000,
      model: 'moonshotai/kimi-k2',
      retryAttempts: 3,
      ...config
    }
  }

  async analyzeWebContent(webResults: WebSearchResult[], topic: string): Promise<Partial<CompetitiveAnalysis>> {
    console.log(`🧠 ${this.agentName}: Analyzing ${webResults.length} web sources for competitive insights`)

    // Simple analysis based on content patterns
    const contentTypes = new Set<string>()
    const commonWords = new Map<string, number>()
    let totalLength = 0

    webResults.forEach(result => {
      // Analyze content types
      if (result.title.toLowerCase().includes('guide')) contentTypes.add('guide')
      if (result.title.toLowerCase().includes('tutorial')) contentTypes.add('tutorial')
      if (result.title.toLowerCase().includes('how to')) contentTypes.add('how-to')
      if (result.title.toLowerCase().includes('tips')) contentTypes.add('tips')
      if (result.title.toLowerCase().includes('best')) contentTypes.add('best-practices')

      // Count content length
      totalLength += result.content.length

      // Extract common words
      const words = result.content.toLowerCase().split(/\s+/)
      words.forEach(word => {
        if (word.length > 4) {
          commonWords.set(word, (commonWords.get(word) || 0) + 1)
        }
      })
    })

    const averageLength = webResults.length > 0 ? Math.round(totalLength / webResults.length) : 1500

    const analysis = {
      patterns: [{
        type: 'informational content',
        frequency: 0.8,
        examples: Array.from(contentTypes).slice(0, 3),
        effectiveness: 0.7
      }],
      gaps: [
        `Comprehensive 2025 perspective on ${topic}`,
        'Practical implementation examples',
        'Step-by-step actionable guidance'
      ],
      opportunities: [
        `Create definitive guide for ${topic}`,
        'Include real-world examples',
        'Provide current 2025 insights'
      ],
      bestPractices: [
        'Use clear headings and structure',
        'Include practical examples',
        'Provide actionable insights',
        'Reference current trends'
      ],
      contentTypes: Array.from(contentTypes),
      averageLength,
      commonStructures: ['introduction', 'main sections', 'conclusion', 'examples']
    }

    console.log(`✅ ${this.agentName}: Web content analysis completed`)
    return analysis
  }

  async analyzeVideoContent(videos: YouTubeVideoData[], topic: string): Promise<{
    videoPatterns: ContentPattern[]
    scriptingInsights: string[]
    engagementTechniques: string[]
  }> {
    console.log(`🎥 ${this.agentName}: Analyzing ${videos.length} YouTube videos for content insights`)
    
    if (videos.length === 0) {
      return {
        videoPatterns: [],
        scriptingInsights: [],
        engagementTechniques: []
      }
    }

    const videoSample = videos.slice(0, 5).map(video => ({
      title: video.title,
      channel: video.channel,
      captions: video.captions.slice(0, 20).map(c => c.text).join(' ').substring(0, 800)
    }))

    const prompt = `You are an expert video content analyst. Analyze these YouTube videos about "${topic}" for content patterns and techniques:

Video Data:
${videoSample.map((video, i) => `
${i + 1}. Title: ${video.title}
Channel: ${video.channel}
Script Sample: ${video.captions}
---`).join('\n')}

Provide analysis in JSON format:
{
  "videoPatterns": [
    {
      "type": "pattern type",
      "frequency": 0.8,
      "examples": ["example1", "example2"],
      "effectiveness": 0.9
    }
  ],
  "scriptingInsights": ["insight1", "insight2"],
  "engagementTechniques": ["technique1", "technique2"]
}

Focus on:
- Common video structures and formats
- Scripting patterns and techniques
- Engagement and retention strategies
- Educational approaches
- Content presentation styles`

    try {
      const response = await this.gemini.generateContent(prompt)
      console.log(`🔍 ${this.agentName}: Raw Gemini response length: ${response.length}`)
      console.log(`🔍 ${this.agentName}: Raw response preview: ${response.substring(0, 200)}...`)

      // Clean the response to extract JSON
      let cleanResponse = response.trim()

      // Remove markdown code blocks if present
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }

      console.log(`🔍 ${this.agentName}: Cleaned response preview: ${cleanResponse.substring(0, 200)}...`)

      const analysis = JSON.parse(cleanResponse)
      console.log(`✅ ${this.agentName}: Video content analysis completed`)
      return analysis
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Video analysis failed:`, error)
      console.warn(`⚠️ ${this.agentName}: Using fallback analysis`)
      return {
        videoPatterns: [{
          type: 'educational format',
          frequency: 0.7,
          examples: ['tutorial style', 'explanation format'],
          effectiveness: 0.8
        }],
        scriptingInsights: ['Clear introduction', 'Step-by-step explanation', 'Practical examples'],
        engagementTechniques: ['Hook in first 15 seconds', 'Visual demonstrations', 'Call to action']
      }
    }
  }

  async identifyContentGaps(
    webAnalysis: Partial<CompetitiveAnalysis>,
    videoAnalysis: any,
    topic: string
  ): Promise<string[]> {
    console.log(`🔍 ${this.agentName}: Identifying content gaps and opportunities`)

    // Generate intelligent gaps based on analysis
    const gaps = [
      `Comprehensive 2025 perspective on ${topic}`,
      `Practical implementation examples for ${topic}`,
      `Data-driven insights and current statistics`,
      `Step-by-step actionable guidance`,
      `Common mistakes and how to avoid them`,
      `Real-world case studies and success stories`,
      `Future trends and predictions for ${topic}`
    ]

    console.log(`✅ ${this.agentName}: Identified ${gaps.length} content gaps`)
    return gaps
  }

  async performCompetitiveAnalysis(
    webResults: WebSearchResult[], 
    videos: YouTubeVideoData[], 
    topic: string
  ): Promise<CompetitiveAnalysis> {
    console.log(`🚀 ${this.agentName}: Starting comprehensive competitive analysis for "${topic}"`)
    
    try {
      // Analyze web content
      const webAnalysis = await this.analyzeWebContent(webResults, topic)

      // Analyze video content with error handling
      let videoAnalysis
      try {
        videoAnalysis = await this.analyzeVideoContent(videos, topic)
      } catch (videoError) {
        console.warn(`⚠️ ${this.agentName}: Video analysis failed, using fallback:`, videoError)
        videoAnalysis = {
          videoPatterns: [],
          scriptingInsights: ['Clear structure', 'Engaging introduction', 'Practical examples'],
          engagementTechniques: ['Strong hook', 'Visual elements', 'Call to action']
        }
      }

      // Identify content gaps
      const gaps = await this.identifyContentGaps(webAnalysis, videoAnalysis, topic)
      
      // Combine insights
      const analysis: CompetitiveAnalysis = {
        patterns: [
          ...(webAnalysis.patterns || []),
          ...(videoAnalysis.videoPatterns || [])
        ],
        gaps,
        opportunities: [
          ...(webAnalysis.opportunities || []),
          'Integrate video insights into written content',
          'Combine web and video best practices',
          'Create multimedia-informed content structure'
        ],
        bestPractices: [
          ...(webAnalysis.bestPractices || []),
          ...(videoAnalysis.scriptingInsights || []),
          ...(videoAnalysis.engagementTechniques || [])
        ],
        contentTypes: webAnalysis.contentTypes || ['article', 'guide'],
        averageLength: webAnalysis.averageLength || 1500,
        commonStructures: webAnalysis.commonStructures || ['introduction', 'main content', 'conclusion']
      }

      console.log(`✅ ${this.agentName}: Competitive analysis completed`)
      console.log(`📊 Analysis summary:`)
      console.log(`   - ${analysis.patterns.length} content patterns identified`)
      console.log(`   - ${analysis.gaps.length} content gaps found`)
      console.log(`   - ${analysis.opportunities.length} opportunities identified`)
      console.log(`   - ${analysis.bestPractices.length} best practices collected`)
      
      return analysis
    } catch (error) {
      console.error(`❌ ${this.agentName}: Competitive analysis failed:`, error)
      throw new Error(`Competitive analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}

import { enhancedTavilySearch } from '@/lib/tools/tavily-search'
import { OpenRouterService } from '@/lib/openrouter'
import { WebSearchResult, AgentConfig } from './types'

export class ResearchAgent {
  private openRouter: OpenRouterService
  private agentName = 'Research Agent'
  private config: AgentConfig

  constructor(config?: Partial<AgentConfig>) {
    this.openRouter = new OpenRouterService()
    this.config = {
      temperature: 0.7,
      maxTokens: 4000,
      model: 'moonshotai/kimi-k2',
      retryAttempts: 3,
      ...config
    }
  }

  async generateSearchQueries(topic: string, targetAudience?: string): Promise<string[]> {
    console.log(`🔍 ${this.agentName}: Generating search queries for "${topic}"`)

    // Use predefined intelligent queries to avoid API issues
    const baseQueries = [
      topic,
      `${topic} 2025 guide`,
      `${topic} best practices`,
      `${topic} tutorial how to`,
      `${topic} examples case studies`,
      `${topic} latest trends`,
      `${topic} comprehensive guide`
    ]

    console.log(`✅ ${this.agentName}: Generated ${baseQueries.length} search queries`)
    return baseQueries
  }

  async conductWebSearch(queries: string[]): Promise<WebSearchResult[]> {
    console.log(`🌐 ${this.agentName}: Conducting web search with ${queries.length} queries`)
    
    const allResults: WebSearchResult[] = []
    const maxResultsPerQuery = 3

    for (const query of queries) {
      try {
        console.log(`🔍 Searching: "${query}"`)
        
        const searchResults = await enhancedTavilySearch.search(query, {
          maxResults: maxResultsPerQuery,
          searchDepth: 'advanced',
          includeAnswer: true,
          includeRawContent: true,
          temporalFocus: 'current'
        })

        if (searchResults && searchResults.results) {
          const formattedResults: WebSearchResult[] = searchResults.results.map((result: any) => ({
            title: result.title || '',
            url: result.url || '',
            content: result.content || result.raw_content || '',
            snippet: result.content?.substring(0, 300) || '',
            score: result.score || 0.5,
            timestamp: new Date().toISOString()
          }))

          allResults.push(...formattedResults)
          console.log(`✅ Found ${formattedResults.length} results for "${query}"`)
        }

        // Small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.warn(`⚠️ Search failed for query "${query}":`, error)
        // Continue with other queries
      }
    }

    // Remove duplicates based on URL
    const uniqueResults = allResults.filter((result, index, self) => 
      index === self.findIndex(r => r.url === result.url)
    )

    // Sort by score and limit total results
    const sortedResults = uniqueResults
      .sort((a, b) => b.score - a.score)
      .slice(0, 20) // Limit to top 20 results

    console.log(`✅ ${this.agentName}: Collected ${sortedResults.length} unique web results`)
    return sortedResults
  }

  async enhanceSearchResults(results: WebSearchResult[], topic: string): Promise<WebSearchResult[]> {
    console.log(`🧠 ${this.agentName}: Using simple relevance scoring`)

    // Simple relevance scoring based on title and content matching
    const enhancedResults = results.map((result) => {
      const topicWords = topic.toLowerCase().split(' ')
      const titleLower = result.title.toLowerCase()
      const contentLower = result.content.toLowerCase()

      let score = 0.5 // Base score

      // Boost score for topic words in title
      topicWords.forEach(word => {
        if (titleLower.includes(word)) score += 0.2
        if (contentLower.includes(word)) score += 0.1
      })

      // Boost for recent content
      if (result.content.includes('2025') || result.content.includes('2024')) {
        score += 0.1
      }

      return {
        ...result,
        score: Math.min(1.0, score)
      }
    })

    // Sort by enhanced scores
    const sortedResults = enhancedResults.sort((a, b) => b.score - a.score)

    console.log(`✅ ${this.agentName}: Simple relevance scoring completed`)
    return sortedResults
  }

  async performResearch(topic: string, targetAudience?: string): Promise<{
    results: WebSearchResult[]
    queries: string[]
  }> {
    console.log(`🚀 ${this.agentName}: Starting comprehensive research for "${topic}"`)
    
    try {
      // Generate search queries
      const queries = await this.generateSearchQueries(topic, targetAudience)
      
      // Conduct web search
      const rawResults = await this.conductWebSearch(queries)
      
      // Enhance results with relevance scoring
      const enhancedResults = await this.enhanceSearchResults(rawResults, topic)
      
      console.log(`✅ ${this.agentName}: Research completed - ${enhancedResults.length} sources found`)
      
      return {
        results: enhancedResults,
        queries
      }
    } catch (error) {
      console.error(`❌ ${this.agentName}: Research failed:`, error)
      throw new Error(`Research failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}

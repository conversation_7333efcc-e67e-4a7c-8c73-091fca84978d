import { Innertube } from 'youtubei.js'
import { YouTubeService } from '@/lib/youtube-service'
import { OpenRouterService } from '@/lib/openrouter'
import { YouTubeVideoData, CaptionSegment, AgentConfig } from './types'

export class YouTubeAgent {
  private youtube: YouTubeService
  private openRouter: OpenRouterService
  private agentName = 'YouTube Agent'
  private config: AgentConfig

  constructor(config?: Partial<AgentConfig>) {
    this.youtube = new YouTubeService()
    this.openRouter = new OpenRouterService()
    this.config = {
      temperature: 0.7,
      maxTokens: 4000,
      model: 'moonshotai/kimi-k2',
      retryAttempts: 3,
      ...config
    }
  }

  async generateVideoSearchQueries(topic: string): Promise<string[]> {
    console.log(`🎥 ${this.agentName}: Generating video search queries for "${topic}"`)
    
    const prompt = `You are an expert YouTube content strategist. Generate 4-5 search queries to find the most relevant and high-quality YouTube videos about: "${topic}"

Requirements:
- Focus on educational, tutorial, and informative content
- Include queries for different content formats (tutorials, explanations, case studies)
- Consider popular YouTube keywords and phrases
- Target videos that would have good captions/transcripts
- Include both broad and specific queries

Return ONLY a JSON array of strings, no other text:
["query1", "query2", "query3", ...]`

    try {
      const result = await this.openRouter.generateContent(
        prompt,
        undefined,
        {
          temperature: this.config.temperature,
          maxTokens: 1000
        },
        'YouTube Query Generation'
      )

      const queries = JSON.parse(result.response.trim())
      console.log(`✅ ${this.agentName}: Generated ${queries.length} video search queries`)
      return queries
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Failed to generate video queries, using fallback`)
      return [
        `${topic} tutorial`,
        `${topic} explained`,
        `${topic} guide 2025`,
        `how to ${topic}`,
        `${topic} tips`
      ]
    }
  }

  async searchYouTubeVideos(queries: string[]): Promise<YouTubeVideoData[]> {
    console.log(`🔍 ${this.agentName}: Searching YouTube with ${queries.length} queries`)
    
    const allVideos: YouTubeVideoData[] = []
    const maxVideosPerQuery = 3

    for (const query of queries) {
      try {
        console.log(`🎥 Searching YouTube: "${query}"`)
        
        // Use InnerTube for video search
        const innertube = await Innertube.create()
        const searchResults = await innertube.search(query, { type: 'video' })
        
        if (searchResults.videos) {
          const videos = searchResults.videos.slice(0, maxVideosPerQuery)
          
          for (const video of videos) {
            try {
              const videoData: YouTubeVideoData = {
                id: video.id || '',
                title: video.title?.text || '',
                channel: video.author?.name || '',
                url: `https://www.youtube.com/watch?v=${video.id}`,
                duration: this.formatDuration(video.duration?.seconds_total || 0),
                views: video.view_count?.text ? this.parseViewCount(video.view_count.text) : 0,
                publishedTime: video.published?.text || '',
                captions: [], // Will be filled later
                relevanceScore: 0.5 // Will be enhanced later
              }
              
              allVideos.push(videoData)
            } catch (videoError) {
              console.warn(`⚠️ Error processing video:`, videoError)
            }
          }
        }

        // Small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        console.warn(`⚠️ YouTube search failed for query "${query}":`, error)
        // Continue with other queries
      }
    }

    // Remove duplicates based on video ID
    const uniqueVideos = allVideos.filter((video, index, self) => 
      index === self.findIndex(v => v.id === video.id)
    )

    console.log(`✅ ${this.agentName}: Found ${uniqueVideos.length} unique videos`)
    return uniqueVideos.slice(0, 10) // Limit to top 10 videos
  }

  async extractCaptions(videos: YouTubeVideoData[]): Promise<YouTubeVideoData[]> {
    console.log(`📝 ${this.agentName}: Extracting captions from ${videos.length} videos`)
    
    const videosWithCaptions: YouTubeVideoData[] = []

    for (const video of videos) {
      try {
        console.log(`📝 Extracting captions for: ${video.title}`)
        
        // Try to extract captions using Supadata API
        const captions = await this.extractCaptionsWithSupadata(video.id)
        
        if (captions && captions.length > 0) {
          videosWithCaptions.push({
            ...video,
            captions
          })
          console.log(`✅ Extracted ${captions.length} caption segments`)
        } else {
          // Try InnerTube as fallback
          const innerTubeCaptions = await this.extractCaptionsWithInnerTube(video.id)
          if (innerTubeCaptions && innerTubeCaptions.length > 0) {
            videosWithCaptions.push({
              ...video,
              captions: innerTubeCaptions
            })
            console.log(`✅ Extracted ${innerTubeCaptions.length} caption segments (InnerTube)`)
          } else {
            console.warn(`⚠️ No captions found for video: ${video.title}`)
          }
        }
      } catch (error) {
        console.warn(`⚠️ Caption extraction failed for video ${video.id}:`, error)
      }
    }

    console.log(`✅ ${this.agentName}: Successfully extracted captions from ${videosWithCaptions.length} videos`)
    return videosWithCaptions
  }

  private async extractCaptionsWithSupadata(videoId: string): Promise<CaptionSegment[]> {
    try {
      // Use the existing YouTube service which has Supadata integration
      const captions = await this.youtube.extractCaptions(videoId)

      if (captions && Array.isArray(captions)) {
        return captions.map((caption: any) => ({
          text: caption.text || '',
          start: caption.start || 0,
          duration: caption.duration || 0
        }))
      }

      return []
    } catch (error) {
      console.warn(`Supadata caption extraction failed for ${videoId}:`, error)
      return []
    }
  }

  private async extractCaptionsWithInnerTube(videoId: string): Promise<CaptionSegment[]> {
    try {
      const innertube = await Innertube.create()
      const videoInfo = await innertube.getInfo(videoId)
      
      if (videoInfo.captions) {
        const transcript = await innertube.getTranscript(videoId)
        
        if (transcript && transcript.content) {
          return transcript.content.body?.initial_segments?.map((segment: any) => ({
            text: segment.snippet?.text || '',
            start: segment.start_ms ? segment.start_ms / 1000 : 0,
            duration: segment.end_ms && segment.start_ms ? 
              (segment.end_ms - segment.start_ms) / 1000 : 0
          })) || []
        }
      }
      
      return []
    } catch (error) {
      console.warn(`InnerTube caption extraction failed for ${videoId}:`, error)
      return []
    }
  }

  private formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  private parseViewCount(viewText: string): number {
    const match = viewText.match(/[\d,]+/)
    if (match) {
      return parseInt(match[0].replace(/,/g, ''))
    }
    return 0
  }

  async performVideoResearch(topic: string): Promise<YouTubeVideoData[]> {
    console.log(`🚀 ${this.agentName}: Starting video research for "${topic}"`)
    
    try {
      // Generate video search queries
      const queries = await this.generateVideoSearchQueries(topic)
      
      // Search for videos
      const videos = await this.searchYouTubeVideos(queries)
      
      // Extract captions
      const videosWithCaptions = await this.extractCaptions(videos)
      
      console.log(`✅ ${this.agentName}: Video research completed - ${videosWithCaptions.length} videos with captions`)
      
      return videosWithCaptions
    } catch (error) {
      console.error(`❌ ${this.agentName}: Video research failed:`, error)
      throw new Error(`Video research failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
